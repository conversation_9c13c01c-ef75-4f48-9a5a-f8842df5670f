#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
load("//bazel:GenTestRules.bzl", "GenTestRules")

java_library(
    name = "auth",
    srcs = glob(["src/main/java/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        "//common",
        "//remoting",
        "//client",
        "@maven//:commons_codec_commons_codec",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:commons_collections_commons_collections",
        "@maven//:com_alibaba_fastjson2_fastjson2",
        "@maven//:org_apache_rocketmq_rocketmq_proto",
        "@maven//:org_slf4j_slf4j_api",
        "@maven//:com_github_ben_manes_caffeine_caffeine",
        "@maven//:io_grpc_grpc_api",
        "@maven//:com_google_protobuf_protobuf_java",
        "@maven//:com_google_protobuf_protobuf_java_util",
        "@maven//:io_netty_netty_all",
        "@maven//:com_google_guava_guava",
        "@maven//:org_apache_rocketmq_rocketmq_rocksdb",
    ],
)

java_library(
    name = "tests",
    srcs = glob(["src/test/java/**/*.java"]),
    resources = glob(["src/test/resources/**/*.yml"]),
    visibility = ["//visibility:public"],
    deps = [
        ":auth",
        "//:test_deps",
        "//common",
        "//remoting",
        "//client",
        "@maven//:commons_codec_commons_codec",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:commons_collections_commons_collections",
        "@maven//:com_alibaba_fastjson2_fastjson2",
        "@maven//:org_apache_rocketmq_rocketmq_proto",
        "@maven//:org_slf4j_slf4j_api",
        "@maven//:com_github_ben_manes_caffeine_caffeine",
        "@maven//:io_grpc_grpc_api",
        "@maven//:com_google_protobuf_protobuf_java",
        "@maven//:com_google_protobuf_protobuf_java_util",
        "@maven//:io_netty_netty_all",
        "@maven//:com_google_guava_guava",
        "@maven//:org_apache_rocketmq_rocketmq_rocksdb",
    ],
)

GenTestRules(
    name = "GeneratedTestRules",
    test_files = glob(["src/test/java/**/*Test.java"]),
    deps = [
        ":tests",
    ],
)
